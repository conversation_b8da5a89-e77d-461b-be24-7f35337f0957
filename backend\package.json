{"name": "subscription-tracker-backend", "version": "1.0.0", "description": "Backend API for Subscription Tracker", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "install-types": "node install-types.js", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "compression": "^1.7.4", "bcrypt": "^5.1.1", "jsonwebtoken": "^9.0.2", "zod": "^3.22.4", "@supabase/supabase-js": "^2.38.0", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "redis": "^4.6.10", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "jspdf": "^2.5.1", "xlsx": "^0.18.5"}, "devDependencies": {"@types/express": "^4.17.21", "@types/node": "^20.10.0", "@types/cors": "^2.8.17", "@types/compression": "^1.7.5", "@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/nodemailer": "^6.4.14", "@types/multer": "^1.4.11", "typescript": "^5.3.2", "ts-node": "^10.9.1", "nodemon": "^3.0.2", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "keywords": ["subscription", "tracker", "api", "express", "typescript"], "author": "", "license": "MIT"}