{"name": "subscription-tracker-backend", "version": "1.0.0", "description": "Backend API for Subscription Tracker", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "install-types": "node install-types.js", "type-check": "tsc --noEmit", "clean": "rm -rf dist"}, "dependencies": {"@supabase/supabase-js": "^2.38.0", "axios": "^1.6.2", "bcrypt": "^5.1.1", "compression": "^1.7.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "jspdf": "^3.0.1", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemailer": "^6.9.7", "redis": "^4.6.10", "xlsx": "^0.18.5", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/compression": "^1.7.5", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/node-cron": "^3.0.11", "@types/nodemailer": "^6.4.14", "jest": "^29.7.0", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "typescript": "^5.3.2"}, "keywords": ["subscription", "tracker", "api", "express", "typescript"], "author": "", "license": "MIT"}