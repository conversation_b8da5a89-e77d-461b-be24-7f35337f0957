import React, { useState, useEffect } from 'react';
import { usePayment, Payment, PaymentFilters } from '../contexts/PaymentContext';
import { useBusiness } from '../contexts/BusinessContext';
import { PaymentTable } from '../components/payment/PaymentTable';
import { PaymentModal } from '../components/payment/PaymentModal';
import { DeletePaymentModal } from '../components/payment/DeletePaymentModal';
import { PaymentCalendar } from '../components/payment/PaymentCalendar';
import { PaymentExport } from '../components/payment/PaymentExport';
import { BusinessSelector } from '../components/business/BusinessSelector';

export const PaymentPage: React.FC = () => {
  const { 
    payments, 
    loading, 
    error, 
    createPayment, 
    updatePayment, 
    deletePayment,
    fetchPayments
  } = usePayment();
  
  const { currentBusiness } = useBusiness();
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [selectedPayment, setSelectedPayment] = useState<Payment | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [viewMode, setViewMode] = useState<'table' | 'calendar'>('table');
  
  const [filters, setFilters] = useState<PaymentFilters>({
    status: undefined,
    currency: undefined,
    payment_method: undefined,
    date_from: undefined,
    date_to: undefined
  });

  useEffect(() => {
    if (currentBusiness) {
      fetchPayments({ business_id: currentBusiness.id, ...filters });
    }
  }, [currentBusiness, filters, fetchPayments]);

  const openCreateModal = () => {
    setSelectedPayment(null);
    setIsModalOpen(true);
  };

  const openEditModal = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsModalOpen(true);
  };

  const openDeleteModal = (payment: Payment) => {
    setSelectedPayment(payment);
    setIsDeleteModalOpen(true);
  };

  const handleModalClose = async () => {
    setIsModalOpen(false);
    setSelectedPayment(null);
    if (currentBusiness) {
      await fetchPayments({ business_id: currentBusiness.id, ...filters });
    }
  };

  const handleDeleteConfirm = async () => {
    if (!selectedPayment) return;
    
    setIsLoading(true);
    try {
      await deletePayment(selectedPayment.id);
      setIsDeleteModalOpen(false);
      setSelectedPayment(null);
      if (currentBusiness) {
        await fetchPayments({ business_id: currentBusiness.id, ...filters });
      }
    } catch (error) {
      console.error('Failed to delete payment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFilterChange = (key: keyof PaymentFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const clearFilters = () => {
    setFilters({
      status: undefined,
      currency: undefined,
      payment_method: undefined,
      date_from: undefined,
      date_to: undefined
    });
  };

  const handlePaymentClick = (payment: Payment) => {
    openEditModal(payment);
  };

  const handleDateClick = (date: Date) => {
    // Set date filter to the clicked date
    const dateStr = date.toISOString().split('T')[0];
    setFilters(prev => ({
      ...prev,
      date_from: dateStr,
      date_to: dateStr
    }));
    setViewMode('table'); // Switch to table view to show filtered results
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Payment Tracking</h1>
        <div className="flex items-center space-x-4">
          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 rounded-lg p-1">
            <button
              onClick={() => setViewMode('table')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'table' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Table
            </button>
            <button
              onClick={() => setViewMode('calendar')}
              className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                viewMode === 'calendar' 
                  ? 'bg-white text-gray-900 shadow-sm' 
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Calendar
            </button>
          </div>
          
          <button
            onClick={() => setIsExportModalOpen(true)}
            className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md"
          >
            Export
          </button>
          <button
            onClick={openCreateModal}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
          >
            Add Payment
          </button>
        </div>
      </div>

      {/* Business Selector */}
      <BusinessSelector />

      {/* No Business Selected */}
      {!currentBusiness && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4">
          <p className="text-yellow-800 text-sm">
            Please select a business to view and manage payments.
          </p>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <p className="text-red-800 text-sm">{error}</p>
        </div>
      )}

      {/* Filters (only show in table mode) */}
      {currentBusiness && viewMode === 'table' && (
        <div className="bg-white shadow rounded-lg p-6">
          <h2 className="text-lg font-medium mb-4">Filters</h2>
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700">Status</label>
              <select
                value={filters.status || ''}
                onChange={(e) => handleFilterChange('status', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">All Statuses</option>
                <option value="completed">Completed</option>
                <option value="pending">Pending</option>
                <option value="failed">Failed</option>
                <option value="refunded">Refunded</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">Currency</label>
              <select
                value={filters.currency || ''}
                onChange={(e) => handleFilterChange('currency', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              >
                <option value="">All Currencies</option>
                <option value="USD">USD</option>
                <option value="GBP">GBP</option>
                <option value="KWD">KWD</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">From Date</label>
              <input
                type="date"
                value={filters.date_from || ''}
                onChange={(e) => handleFilterChange('date_from', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700">To Date</label>
              <input
                type="date"
                value={filters.date_to || ''}
                onChange={(e) => handleFilterChange('date_to', e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md"
              />
            </div>
            <div className="flex items-end">
              <button
                onClick={clearFilters}
                className="w-full bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded-md"
              >
                Clear Filters
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Content */}
      {currentBusiness && (
        <>
          {viewMode === 'table' ? (
            <PaymentTable
              payments={payments}
              onEdit={openEditModal}
              onDelete={openDeleteModal}
              loading={loading}
            />
          ) : (
            <PaymentCalendar
              onPaymentClick={handlePaymentClick}
              onDateClick={handleDateClick}
            />
          )}
        </>
      )}

      {/* Modals */}
      <PaymentModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        payment={selectedPayment || undefined}
        businessId={currentBusiness?.id}
      />

      <DeletePaymentModal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        onConfirm={handleDeleteConfirm}
        payment={selectedPayment}
        loading={isLoading}
      />

      <PaymentExport
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
      />
    </div>
  );
};
