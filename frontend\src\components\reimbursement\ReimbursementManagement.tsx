import React, { useState, useEffect } from 'react';
import { ReimbursementTable } from './ReimbursementTable';
import { ReimbursementModal } from './ReimbursementModal';
import { ReimbursementDetail } from './ReimbursementDetail';
import { ReimbursementCalendar } from './ReimbursementCalendar';
import { BulkProcessor } from './BulkProcessor';
import { ReimbursementDashboard } from './ReimbursementDashboard';
import { ReimbursementExport } from './ReimbursementExport';
import { useReimbursement, Reimbursement } from '../../contexts/ReimbursementContext';
import { useBusiness } from '../../contexts/BusinessContext';

type ViewMode = 'table' | 'calendar' | 'dashboard' | 'bulk';

export const ReimbursementManagement: React.FC = () => {
  const {
    reimbursements,
    loading,
    error,
    fetchReimbursements,
    updateReimbursementStatus,
    deleteReimbursement
  } = useReimbursement();
  
  const { currentBusiness } = useBusiness();
  const [viewMode, setViewMode] = useState<ViewMode>('table');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isExportModalOpen, setIsExportModalOpen] = useState(false);
  const [selectedReimbursement, setSelectedReimbursement] = useState<Reimbursement | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [filters, setFilters] = useState({
    business_id: '',
    status: '' as '' | 'pending' | 'approved' | 'received',
    date_from: '',
    date_to: ''
  });

  useEffect(() => {
    // Fetch reimbursements when component mounts or filters change
    const activeFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    );
    fetchReimbursements(activeFilters);
  }, [fetchReimbursements, filters]);

  const handleCreateReimbursement = () => {
    setIsModalOpen(true);
  };

  const handleEditReimbursement = (reimbursement: Reimbursement) => {
    setSelectedReimbursement(reimbursement);
    setIsModalOpen(true);
  };

  const handleViewReimbursement = (reimbursement: Reimbursement) => {
    setSelectedReimbursement(reimbursement);
    setIsDetailModalOpen(true);
  };

  const handleDeleteReimbursement = async (reimbursement: Reimbursement) => {
    if (window.confirm('Are you sure you want to delete this reimbursement? This action cannot be undone.')) {
      try {
        await deleteReimbursement(reimbursement.id);
      } catch (error) {
        console.error('Failed to delete reimbursement:', error);
        alert('Failed to delete reimbursement. Please try again.');
      }
    }
  };

  const handleStatusChange = async (
    reimbursement: Reimbursement,
    newStatus: 'pending' | 'approved' | 'received'
  ) => {
    if (reimbursement.status === newStatus) return;

    const statusConfirmations = {
      approved: 'Are you sure you want to approve this reimbursement?',
      received: 'Are you sure you want to mark this reimbursement as received?',
      pending: 'Are you sure you want to change this reimbursement back to pending?'
    };

    if (window.confirm(statusConfirmations[newStatus])) {
      try {
        await updateReimbursementStatus(reimbursement.id, newStatus);
      } catch (error) {
        console.error('Failed to update status:', error);
        alert('Failed to update reimbursement status. Please try again.');
      }
    }
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
    setSelectedReimbursement(null);
  };

  const handleDetailModalClose = () => {
    setIsDetailModalOpen(false);
    setSelectedReimbursement(null);
  };

  const handleFilterChange = (key: string, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleCalendarDateClick = (date: Date, dayReimbursements: any) => {
    const allDayReimbursements = [
      ...dayReimbursements.expected,
      ...dayReimbursements.received
    ];
    
    if (allDayReimbursements.length === 1) {
      handleViewReimbursement(allDayReimbursements[0]);
    } else if (allDayReimbursements.length > 1) {
      // Show a list of reimbursements for that day
      const dateStr = date.toLocaleDateString();
      alert(`${allDayReimbursements.length} reimbursements on ${dateStr}:\n\n${
        allDayReimbursements.map(r => `• ${r.reference_number} - KWD ${r.amount.toFixed(3)}`).join('\n')
      }`);
    }
  };

  const handleBulkComplete = () => {
    setSelectedIds([]);
    // Refresh data
    const activeFilters = Object.fromEntries(
      Object.entries(filters).filter(([_, value]) => value !== '')
    );
    fetchReimbursements(activeFilters);
  };

  // Get summary statistics
  const stats = {
    total: reimbursements.length,
    pending: reimbursements.filter(r => r.status === 'pending').length,
    approved: reimbursements.filter(r => r.status === 'approved').length,
    received: reimbursements.filter(r => r.status === 'received').length,
    totalAmount: reimbursements.reduce((sum, r) => sum + r.amount, 0),
    pendingAmount: reimbursements.filter(r => r.status === 'pending').reduce((sum, r) => sum + r.amount, 0)
  };

  const overdue = reimbursements.filter(r => 
    (r.status === 'pending' || r.status === 'approved') && 
    new Date(r.expected_date) < new Date()
  ).length;

  if (loading && reimbursements.length === 0) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading reimbursements...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="sm:flex sm:items-center sm:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Reimbursement Management</h1>
          <p className="mt-2 text-sm text-gray-700">
            Comprehensive reimbursement tracking and management system.
          </p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-3">
          {/* View Mode Selector */}
          <div className="flex rounded-md shadow-sm">
            <button
              onClick={() => setViewMode('dashboard')}
              className={`px-3 py-2 text-sm font-medium rounded-l-md border ${
                viewMode === 'dashboard'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Dashboard
            </button>
            <button
              onClick={() => setViewMode('table')}
              className={`px-3 py-2 text-sm font-medium border-t border-b ${
                viewMode === 'table'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Table
            </button>
            <button
              onClick={() => setViewMode('calendar')}
              className={`px-3 py-2 text-sm font-medium border-t border-b ${
                viewMode === 'calendar'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Calendar
            </button>
            <button
              onClick={() => setViewMode('bulk')}
              className={`px-3 py-2 text-sm font-medium rounded-r-md border ${
                viewMode === 'bulk'
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              Bulk Actions
            </button>
          </div>

          <button
            onClick={() => setIsExportModalOpen(true)}
            className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Export
          </button>

          <button
            onClick={handleCreateReimbursement}
            className="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg className="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
            New Reimbursement
          </button>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
          </div>
        </div>
      )}

      {/* Quick Stats - Only show for non-dashboard views */}
      {viewMode !== 'dashboard' && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Total Requests</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.total}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      {stats.pending}
                      {overdue > 0 && (
                        <span className="ml-2 text-sm text-red-600">({overdue} overdue)</span>
                      )}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Approved</dt>
                    <dd className="text-lg font-medium text-gray-900">{stats.approved}</dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div className="bg-white overflow-hidden shadow rounded-lg">
            <div className="p-5">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <svg className="h-6 w-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                  </svg>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">Pending Amount</dt>
                    <dd className="text-lg font-medium text-gray-900">
                      KWD {stats.pendingAmount.toFixed(3)}
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      {viewMode === 'dashboard' && (
        <ReimbursementDashboard />
      )}

      {viewMode === 'table' && (
        <>
          {/* Filters */}
          <div className="bg-white shadow rounded-lg p-4">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div>
                <label htmlFor="status-filter" className="block text-sm font-medium text-gray-700">
                  Status
                </label>
                <select
                  id="status-filter"
                  value={filters.status}
                  onChange={(e) => handleFilterChange('status', e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="received">Received</option>
                </select>
              </div>

              <div>
                <label htmlFor="date-from" className="block text-sm font-medium text-gray-700">
                  From Date
                </label>
                <input
                  type="date"
                  id="date-from"
                  value={filters.date_from}
                  onChange={(e) => handleFilterChange('date_from', e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div>
                <label htmlFor="date-to" className="block text-sm font-medium text-gray-700">
                  To Date
                </label>
                <input
                  type="date"
                  id="date-to"
                  value={filters.date_to}
                  onChange={(e) => handleFilterChange('date_to', e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              <div className="flex items-end">
                <button
                  onClick={() => setFilters({
                    business_id: '',
                    status: '',
                    date_from: '',
                    date_to: ''
                  })}
                  className="w-full px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Clear Filters
                </button>
              </div>
            </div>
          </div>

          {/* Reimbursements Table */}
          <div className="bg-white shadow rounded-lg">
            <ReimbursementTable
              reimbursements={reimbursements}
              onEdit={handleEditReimbursement}
              onDelete={handleDeleteReimbursement}
              onView={handleViewReimbursement}
              onStatusChange={handleStatusChange}
              loading={loading}
            />
          </div>
        </>
      )}

      {viewMode === 'calendar' && (
        <ReimbursementCalendar
          reimbursements={reimbursements}
          onDateClick={handleCalendarDateClick}
          onReimbursementClick={handleViewReimbursement}
        />
      )}

      {viewMode === 'bulk' && (
        <BulkProcessor
          reimbursements={reimbursements}
          selectedIds={selectedIds}
          onSelectionChange={setSelectedIds}
          onComplete={handleBulkComplete}
        />
      )}

      {/* Create/Edit Modal */}
      <ReimbursementModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
      />

      {/* Detail Modal */}
      {isDetailModalOpen && selectedReimbursement && (
        <div className="fixed inset-0 z-50 overflow-y-auto">
          <div className="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
              onClick={handleDetailModalClose}
            />
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full">
              <ReimbursementDetail
                reimbursement={selectedReimbursement}
                onClose={handleDetailModalClose}
                onEdit={() => {
                  handleDetailModalClose();
                  handleEditReimbursement(selectedReimbursement);
                }}
              />
            </div>
          </div>
        </div>
      )}

      {/* Export Modal */}
      <ReimbursementExport
        isOpen={isExportModalOpen}
        onClose={() => setIsExportModalOpen(false)}
      />
    </div>
  );
};