// Type definitions for packages that don't have proper @types packages

declare module 'aria-query' {
  export const elementRoles: Map<string, Set<string>>;
  export const roleElements: Map<string, Set<string>>;
  export const roles: Map<string, any>;
}

declare module 'graceful-fs' {
  import * as fs from 'fs';
  export = fs;
}

declare module 'http-errors' {
  interface HttpError extends Error {
    status?: number;
    statusCode?: number;
    expose?: boolean;
    headers?: { [key: string]: string };
  }
  
  function createError(status: number, message?: string): HttpError;
  function createError(message: string): HttpError;
  function createError(): HttpError;
  
  export = createError;
}

declare module 'stack-utils' {
  interface StackUtilsOptions {
    internals?: string[];
    ignoredPackages?: string[];
    cwd?: string;
    wrapCallSite?: (callSite: any) => any;
  }
  
  class StackUtils {
    constructor(options?: StackUtilsOptions);
    clean(stack: string): string;
    capture(limit?: number, startStackFunction?: Function): any[];
  }
  
  export = StackUtils;
}

// Babel types (these shouldn't be in backend but declaring to avoid errors)
declare module '@babel/core' {
  export interface TransformOptions {
    [key: string]: any;
  }
  export function transform(code: string, options?: TransformOptions): any;
}

declare module '@babel/generator' {
  export default function generate(ast: any, options?: any): { code: string; map?: any };
}

declare module '@babel/template' {
  export default function template(code: string): any;
}

declare module '@babel/traverse' {
  export default function traverse(ast: any, visitor: any): void;
}

// Istanbul types (testing coverage - shouldn't be in backend)
declare module 'istanbul-lib-coverage' {
  export interface CoverageMap {
    [key: string]: any;
  }
  export function createCoverageMap(data?: any): CoverageMap;
}

declare module 'istanbul-lib-report' {
  export interface Context {
    [key: string]: any;
  }
  export function createContext(options?: any): Context;
}

declare module 'istanbul-reports' {
  export function create(name: string, options?: any): any;
}

// Testing library types (shouldn't be in backend)
declare module '@testing-library/jest-dom' {
  export {};
}

declare module 'yargs-parser' {
  interface Arguments {
    [key: string]: any;
  }
  function parser(args: string | string[], options?: any): Arguments;
  export = parser;
}

declare module 'prop-types' {
  export interface Validator<T> {
    (props: any, propName: string, componentName: string): Error | null;
  }
  
  export const string: Validator<string>;
  export const number: Validator<number>;
  export const bool: Validator<boolean>;
  export const func: Validator<Function>;
  export const object: Validator<object>;
  export const array: Validator<any[]>;
  export const node: Validator<any>;
  export const element: Validator<any>;
  export const instanceOf: (expectedClass: any) => Validator<any>;
  export const oneOf: (types: any[]) => Validator<any>;
  export const oneOfType: (types: Validator<any>[]) => Validator<any>;
  export const arrayOf: (type: Validator<any>) => Validator<any[]>;
  export const objectOf: (type: Validator<any>) => Validator<object>;
  export const shape: (types: { [key: string]: Validator<any> }) => Validator<object>;
}