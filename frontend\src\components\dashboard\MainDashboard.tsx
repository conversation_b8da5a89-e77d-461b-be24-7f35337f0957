import React, { useState, useEffect, useMemo } from 'react';
import { Link } from 'react-router-dom';
import { 
  Card, 
  MetricCard, 
  KPICard,
  SectionHeader, 
  QuickAction, 
  EmptyState, 
  StatsGrid,
  ChartContainer,
  Alert
} from './DashboardComponents';
import { useBusiness } from '../../contexts/BusinessContext';
import { useSubscription } from '../../contexts/SubscriptionContext';
import { usePayment } from '../../contexts/PaymentContext';
import { useReimbursement } from '../../contexts/ReimbursementContext';
import { DashboardCharts } from './DashboardCharts';

export const MainDashboard: React.FC = () => {
  const { businesses, currentBusiness } = useBusiness();
  const { subscriptions, loading: subscriptionsLoading, fetchSubscriptions } = useSubscription();
  const { payments, loading: paymentsLoading, fetchPayments } = usePayment();
  const { reimbursements, loading: reimbursementsLoading, fetchReimbursements } = useReimbursement();
  const [showAlert, setShowAlert] = useState(true);
  const [showCharts, setShowCharts] = useState(false);

  useEffect(() => {
    // Fetch data when component mounts or current business changes
    if (currentBusiness) {
      fetchSubscriptions({ business_id: currentBusiness.id });
      fetchPayments({ business_id: currentBusiness.id });
      fetchReimbursements({ business_id: currentBusiness.id });
    }
  }, [currentBusiness, fetchSubscriptions, fetchPayments, fetchReimbursements]);

  // Calculate current period metrics
  const currentPeriodMetrics = useMemo(() => {
    const activeSubscriptions = subscriptions.filter(s => s.status === 'active');
    const totalMonthlySpend = activeSubscriptions.reduce((sum, sub) => {
      const monthlyAmount = sub.billing_frequency === 'monthly' ? sub.amount :
                           sub.billing_frequency === 'quarterly' ? sub.amount / 3 :
                           sub.amount / 12;
      return sum + monthlyAmount;
    }, 0);

    const completedPayments = payments.filter(p => p.status === 'completed');
    const thisMonthPayments = completedPayments.filter(p => {
      const paymentDate = new Date(p.payment_date);
      const now = new Date();
      return paymentDate.getMonth() === now.getMonth() && 
             paymentDate.getFullYear() === now.getFullYear();
    });
    const thisMonthSpend = thisMonthPayments.reduce((sum, p) => sum + p.amount, 0);

    const pendingReimbursements = reimbursements.filter(r => r.status === 'pending');
    const pendingAmount = pendingReimbursements.reduce((sum, r) => sum + r.amount, 0);

    const overdueReimbursements = reimbursements.filter(r => 
      (r.status === 'pending' || r.status === 'approved') && 
      new Date(r.expected_date) < new Date()
    );

    // Calculate upcoming payments (next 7 days)
    const upcomingPayments = subscriptions.filter(s => {
      if (s.status !== 'active') return false;
      const nextPayment = new Date(s.next_payment_date);
      const now = new Date();
      const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
      return nextPayment >= now && nextPayment <= weekFromNow;
    });

    return {
      totalBusinesses: businesses.length,
      activeSubscriptionsCount: activeSubscriptions.length,
      totalSubscriptions: subscriptions.length,
      monthlySpend: totalMonthlySpend,
      thisMonthSpend,
      pendingReimbursementsCount: pendingReimbursements.length,
      pendingReimbursementAmount: pendingAmount,
      overdueReimbursementsCount: overdueReimbursements.length,
      upcomingPayments: upcomingPayments.length,
      totalPayments: completedPayments.length,
      activeSubscriptions,
      completedPayments,
      thisMonthPayments,
      pendingReimbursements,
      overdueReimbursements
    };
  }, [businesses, subscriptions, payments, reimbursements]);

  // Calculate previous period metrics for trend analysis
  const previousPeriodMetrics = useMemo(() => {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);
    const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
    
    const lastMonthPayments = payments.filter(p => {
      const paymentDate = new Date(p.payment_date);
      return p.status === 'completed' && 
             paymentDate >= lastMonth && 
             paymentDate <= lastMonthEnd;
    });
    
    const lastMonthSpend = lastMonthPayments.reduce((sum, p) => sum + p.amount, 0);
    
    // Calculate subscription changes
    const lastMonthActiveSubscriptions = subscriptions.filter(s => {
      const createdDate = new Date(s.created_at);
      return s.status === 'active' && createdDate <= lastMonthEnd;
    });
    
    return {
      lastMonthSpend,
      lastMonthActiveSubscriptions: lastMonthActiveSubscriptions.length,
      lastMonthPayments: lastMonthPayments.length
    };
  }, [payments, subscriptions]);

  // Calculate advanced KPIs
  const advancedKPIs = useMemo(() => {
    const { thisMonthSpend, monthlySpend, activeSubscriptions, totalPayments } = currentPeriodMetrics;
    const { lastMonthSpend, lastMonthActiveSubscriptions, lastMonthPayments } = previousPeriodMetrics;
    
    // Spending trends
    const spendingTrend = lastMonthSpend > 0 ? 
      ((thisMonthSpend - lastMonthSpend) / lastMonthSpend) * 100 : 0;
    
    // Subscription growth
    const subscriptionGrowth = lastMonthActiveSubscriptions > 0 ? 
      ((activeSubscriptions.length - lastMonthActiveSubscriptions) / lastMonthActiveSubscriptions) * 100 : 0;
    
    // Payment efficiency (avg time to complete payments)
    const avgPaymentTime = payments.length > 0 ? 
      payments.reduce((sum, p) => {
        const created = new Date(p.created_at);
        const paid = new Date(p.payment_date);
        return sum + (paid.getTime() - created.getTime());
      }, 0) / payments.length / (1000 * 60 * 60 * 24) : 0; // days
    
    // Reimbursement efficiency
    const avgReimbursementTime = reimbursements.filter(r => r.status === 'received').length > 0 ? 
      reimbursements
        .filter(r => r.status === 'received')
        .reduce((sum, r) => {
          const requested = new Date(r.request_date);
          const received = new Date(r.updated_at);
          return sum + (received.getTime() - requested.getTime());
        }, 0) / reimbursements.filter(r => r.status === 'received').length / (1000 * 60 * 60 * 24) : 0; // days
    
    // Cost efficiency (spend per subscription)
    const costPerSubscription = activeSubscriptions.length > 0 ? monthlySpend / activeSubscriptions.length : 0;
    
    // Payment frequency trend
    const paymentFrequencyTrend = lastMonthPayments > 0 ? 
      ((totalPayments - lastMonthPayments) / lastMonthPayments) * 100 : 0;
    
    return {
      spendingTrend,
      subscriptionGrowth,
      avgPaymentTime,
      avgReimbursementTime,
      costPerSubscription,
      paymentFrequencyTrend
    };
  }, [currentPeriodMetrics, previousPeriodMetrics, payments, reimbursements]);

  const quickActions = [
    {
      title: 'Add Subscription',
      description: 'Track a new subscription service',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-6 w-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      ),
      href: '/subscriptions?action=create',
      color: 'blue' as const
    },
    {
      title: 'Record Payment',
      description: 'Log a new payment transaction',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-6 w-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      href: '/payments?action=create',
      color: 'green' as const
    },
    {
      title: 'Request Reimbursement',
      description: 'Submit a new reimbursement request',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-6 w-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
        </svg>
      ),
      href: '/reimbursements?action=create',
      color: 'purple' as const
    },
    {
      title: 'View Reports',
      description: 'Generate and download reports',
      icon: (
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" className="h-6 w-6">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 17v-2m3 2v-4m3 4v-6m2 10H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      href: '/reports',
      color: 'yellow' as const
    }
  ];

  const isLoading = subscriptionsLoading || paymentsLoading || reimbursementsLoading;

  if (!currentBusiness) {
    return (
      <div className="space-y-6">
        {showAlert && (
          <Alert
            type="info"
            title="Select a Business"
            message="Please select a business from the sidebar to view your dashboard."
            onDismiss={() => setShowAlert(false)}
          />
        )}
        
        <EmptyState
          title="No Business Selected"
          description="Select a business from the sidebar to view your dashboard and start managing subscriptions."
          icon={
            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
            </svg>
          }
          action={
            <Link
              to="/businesses"
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700"
            >
              Manage Businesses
            </Link>
          }
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Alerts */}
      {currentPeriodMetrics.overdueReimbursementsCount > 0 && showAlert && (
        <Alert
          type="warning"
          title={`${currentPeriodMetrics.overdueReimbursementsCount} Overdue Reimbursement${currentPeriodMetrics.overdueReimbursementsCount !== 1 ? 's' : ''}`}
          message="Some reimbursements are past their expected dates. Please review and take action."
          onDismiss={() => setShowAlert(false)}
        />
      )}

      {/* Key Metrics */}
      <div>
        <SectionHeader
          title="Overview"
          subtitle={`Dashboard for ${currentBusiness.name}`}
          className="mb-4"
        />
        
        <StatsGrid columns={4}>
          <MetricCard
            title="Active Subscriptions"
            value={isLoading ? '...' : currentPeriodMetrics.activeSubscriptionsCount}
            subtitle={`${currentPeriodMetrics.totalSubscriptions} total`}
            color="blue"
            loading={isLoading}
            trend={!isLoading && advancedKPIs.subscriptionGrowth !== 0 ? {
              value: Math.abs(advancedKPIs.subscriptionGrowth),
              label: 'vs last month',
              isPositive: advancedKPIs.subscriptionGrowth > 0
            } : undefined}
            icon={
              <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
              </svg>
            }
            onClick={() => window.location.href = '/subscriptions'}
          />
          
          <MetricCard
            title="Monthly Spend"
            value={isLoading ? '...' : currentPeriodMetrics.monthlySpend}
            format="currency"
            subtitle="Estimated monthly cost"
            color="green"
            loading={isLoading}
            icon={
              <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            }
          />
          
          <MetricCard
            title="This Month"
            value={isLoading ? '...' : currentPeriodMetrics.thisMonthSpend}
            format="currency"
            subtitle={`${currentPeriodMetrics.totalPayments} payments`}
            color="purple"
            loading={isLoading}
            trend={!isLoading && advancedKPIs.spendingTrend !== 0 ? {
              value: Math.abs(advancedKPIs.spendingTrend),
              label: 'vs last month',
              isPositive: advancedKPIs.spendingTrend < 0 // Lower spending is positive
            } : undefined}
            icon={
              <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            }
            onClick={() => window.location.href = '/payments'}
          />
          
          <MetricCard
            title="Pending Reimbursements"
            value={isLoading ? '...' : currentPeriodMetrics.pendingReimbursementAmount}
            format="currency"
            subtitle={`${currentPeriodMetrics.pendingReimbursementsCount} requests`}
            color={currentPeriodMetrics.overdueReimbursementsCount > 0 ? 'red' : 'yellow'}
            loading={isLoading}
            badge={currentPeriodMetrics.overdueReimbursementsCount > 0 ? {
              text: `${currentPeriodMetrics.overdueReimbursementsCount} overdue`,
              variant: 'danger'
            } : undefined}
            icon={
              <svg className="h-6 w-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            }
            onClick={() => window.location.href = '/reimbursements'}
          />
        </StatsGrid>
      </div>

      {/* Quick Actions */}
      <div>
        <SectionHeader
          title="Quick Actions"
          subtitle="Common tasks and operations"
          className="mb-4"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {quickActions.map((action, index) => (
            <QuickAction
              key={index}
              title={action.title}
              description={action.description}
              icon={action.icon}
              href={action.href}
              color={action.color}
            />
          ))}
        </div>
      </div>

      {/* Advanced KPIs */}
      <div>
        <SectionHeader
          title="Business Intelligence"
          subtitle="Advanced metrics and performance indicators"
          className="mb-4"
        />
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <KPICard
            title="Financial Efficiency"
            primaryValue={{
              label: "Cost per Subscription",
              value: isLoading ? 0 : advancedKPIs.costPerSubscription,
              format: "currency"
            }}
            secondaryValues={[
              {
                label: "Monthly Budget",
                value: isLoading ? 0 : currentPeriodMetrics.monthlySpend,
                format: "currency"
              },
              {
                label: "Active Services",
                value: isLoading ? 0 : currentPeriodMetrics.activeSubscriptionsCount
              }
            ]}
            color="green"
            loading={isLoading}
            icon={
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
              </svg>
            }
          />
          
          <KPICard
            title="Payment Performance"
            primaryValue={{
              label: "Avg Processing Time",
              value: isLoading ? 0 : Math.round(advancedKPIs.avgPaymentTime),
              format: "number"
            }}
            secondaryValues={[
              {
                label: "This Month",
                value: isLoading ? 0 : currentPeriodMetrics.thisMonthPayments.length
              },
              {
                label: "Total Processed",
                value: isLoading ? 0 : currentPeriodMetrics.totalPayments
              }
            ]}
            trend={!isLoading && advancedKPIs.paymentFrequencyTrend !== 0 ? {
              value: Math.abs(advancedKPIs.paymentFrequencyTrend),
              label: "payment frequency",
              isPositive: advancedKPIs.paymentFrequencyTrend > 0,
              period: "vs last month"
            } : undefined}
            color="blue"
            loading={isLoading}
            alert={advancedKPIs.avgPaymentTime > 7 ? {
              message: "Payment processing time is above average",
              type: "warning"
            } : undefined}
            icon={
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
          
          <KPICard
            title="Reimbursement Efficiency"
            primaryValue={{
              label: "Avg Resolution Time",
              value: isLoading ? 0 : Math.round(advancedKPIs.avgReimbursementTime),
              format: "number"
            }}
            secondaryValues={[
              {
                label: "Pending Amount",
                value: isLoading ? 0 : currentPeriodMetrics.pendingReimbursementAmount,
                format: "currency"
              },
              {
                label: "Overdue Count",
                value: isLoading ? 0 : currentPeriodMetrics.overdueReimbursementsCount
              }
            ]}
            color={currentPeriodMetrics.overdueReimbursementsCount > 0 ? 'red' : 'purple'}
            loading={isLoading}
            alert={currentPeriodMetrics.overdueReimbursementsCount > 0 ? {
              message: `${currentPeriodMetrics.overdueReimbursementsCount} reimbursements are overdue`,
              type: "error"
            } : advancedKPIs.avgReimbursementTime > (currentBusiness?.reimbursement_timeline || 30) ? {
              message: "Resolution time exceeds business timeline",
              type: "warning"
            } : undefined}
            icon={
              <svg className="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            }
          />
        </div>
      </div>

      {/* Charts Section */}
      <div>
        <SectionHeader
          title="Analytics & Insights"
          subtitle="Interactive charts and data visualization"
          action={
            <button
              onClick={() => setShowCharts(!showCharts)}
              className={`inline-flex items-center px-4 py-2 border rounded-md text-sm font-medium transition-colors ${
                showCharts
                  ? 'border-blue-300 text-blue-700 bg-blue-50 hover:bg-blue-100'
                  : 'border-gray-300 text-gray-700 bg-white hover:bg-gray-50'
              }`}
            >
              <svg 
                className={`mr-2 h-4 w-4 transform transition-transform ${showCharts ? 'rotate-180' : ''}`} 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              {showCharts ? 'Hide Charts' : 'Show Charts'}
            </button>
          }
          className="mb-4"
        />
        
        {showCharts && <DashboardCharts />}
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Upcoming Payments */}
        <Card>
          <SectionHeader
            title="Upcoming Payments"
            subtitle="Next 7 days"
            action={
              <Link
                to="/subscriptions"
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                View all
              </Link>
            }
            className="mb-4"
          />
          
          {isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="animate-pulse flex items-center">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="ml-auto h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              ))}
            </div>
          ) : currentPeriodMetrics.upcomingPayments === 0 ? (
            <EmptyState
              title="No upcoming payments"
              description="All payments are up to date for the next 7 days."
              icon={
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />
          ) : (
            <div className="space-y-3">
              {subscriptions
                .filter(s => {
                  if (s.status !== 'active') return false;
                  const nextPayment = new Date(s.next_payment_date);
                  const now = new Date();
                  const weekFromNow = new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000);
                  return nextPayment >= now && nextPayment <= weekFromNow;
                })
                .slice(0, 5)
                .map(subscription => (
                  <div key={subscription.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {subscription.service_name}
                      </p>
                      <p className="text-xs text-gray-500">
                        Due {new Date(subscription.next_payment_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-sm font-medium text-gray-900">
                      {subscription.currency} {subscription.amount.toFixed(2)}
                    </div>
                  </div>
                ))}
            </div>
          )}
        </Card>

        {/* Recent Reimbursements */}
        <Card>
          <SectionHeader
            title="Recent Reimbursements"
            subtitle="Latest requests"
            action={
              <Link
                to="/reimbursements"
                className="text-sm text-blue-600 hover:text-blue-500"
              >
                View all
              </Link>
            }
            className="mb-4"
          />
          
          {isLoading ? (
            <div className="space-y-3">
              {[1, 2, 3].map(i => (
                <div key={i} className="animate-pulse flex items-center">
                  <div className="h-4 bg-gray-200 rounded w-1/3"></div>
                  <div className="ml-auto h-4 bg-gray-200 rounded w-1/4"></div>
                </div>
              ))}
            </div>
          ) : reimbursements.length === 0 ? (
            <EmptyState
              title="No reimbursements"
              description="Create your first reimbursement request to get started."
              action={
                <Link
                  to="/reimbursements?action=create"
                  className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-blue-700 bg-blue-100 hover:bg-blue-200"
                >
                  Create Request
                </Link>
              }
            />
          ) : (
            <div className="space-y-3">
              {reimbursements
                .sort((a, b) => new Date(b.request_date).getTime() - new Date(a.request_date).getTime())
                .slice(0, 5)
                .map(reimbursement => (
                  <div key={reimbursement.id} className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {reimbursement.reference_number}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(reimbursement.request_date).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className={`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${
                        reimbursement.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        reimbursement.status === 'approved' ? 'bg-blue-100 text-blue-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {reimbursement.status}
                      </span>
                      <span className="text-sm font-medium text-gray-900">
                        KWD {reimbursement.amount.toFixed(3)}
                      </span>
                    </div>
                  </div>
                ))}
            </div>
          )}
        </Card>
      </div>
    </div>
  );
};