import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { useBusiness, Business, BusinessStats } from '../contexts/BusinessContext';
import { useSubscription, Subscription } from '../contexts/SubscriptionContext';
import { usePayment, Payment } from '../contexts/PaymentContext';
import { BusinessStats as BusinessStatsComponent } from '../components/business/BusinessStats';
import { BusinessForm } from '../components/business/BusinessForm';
import { BusinessModal } from '../components/business/BusinessModal';
import { SubscriptionTable } from '../components/subscription/SubscriptionTable';
import { PaymentTable } from '../components/payment/PaymentTable';

export const BusinessDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { businesses, loading, error, updateBusiness, deleteBusiness, getBusinessStats } = useBusiness();
  const { subscriptions, fetchSubscriptions } = useSubscription();
  const { payments, fetchPayments } = usePayment();
  
  const [business, setBusiness] = useState<Business | null>(null);
  const [stats, setStats] = useState<BusinessStats | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [statsLoading, setStatsLoading] = useState(false);

  useEffect(() => {
    if (id && businesses.length > 0) {
      const foundBusiness = businesses.find(b => b.id === id);
      if (foundBusiness) {
        setBusiness(foundBusiness);
        loadBusinessStats(id);
        fetchSubscriptions({ business_id: id });
        fetchPayments({ business_id: id });
      } else {
        navigate('/businesses');
      }
    }
  }, [id, businesses, navigate, fetchSubscriptions]);

  const loadBusinessStats = async (businessId: string) => {
    setStatsLoading(true);
    try {
      const businessStats = await getBusinessStats(businessId);
      setStats(businessStats);
    } catch (error) {
      console.error('Failed to load business stats:', error);
    } finally {
      setStatsLoading(false);
    }
  };

  const handleEdit = async (updates: any) => {
    try {
      await updateBusiness(id!, updates);
      setIsEditModalOpen(false);
      // Refresh business data
      const updatedBusiness = businesses.find(b => b.id === id);
      setBusiness(updatedBusiness || null);
    } catch (error) {
      console.error('Failed to update business:', error);
    }
  };

  const handleDelete = async () => {
    try {
      await deleteBusiness(id!);
      navigate('/businesses');
    } catch (error) {
      console.error('Failed to delete business:', error);
    }
  };

  const businessSubscriptions = subscriptions.filter(sub => sub.business_id === id);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-md p-4">
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  if (!business) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">Business not found</p>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Header */}
      <div className="bg-white shadow rounded-lg mb-6">
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">{business.name}</h1>
              {business.description && (
                <p className="text-gray-600 mt-1">{business.description}</p>
              )}
            </div>
            <div className="flex space-x-3">
              <button
                onClick={() => setIsEditModalOpen(true)}
                className="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Edit
              </button>
              <button
                onClick={() => setIsDeleteModalOpen(true)}
                className="px-4 py-2 border border-red-300 rounded-md shadow-sm text-sm font-medium text-red-700 bg-white hover:bg-red-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500"
              >
                Delete
              </button>
            </div>
          </div>
        </div>

        {/* Business Details */}
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {business.address && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Address</dt>
                <dd className="mt-1 text-sm text-gray-900">{business.address}</dd>
              </div>
            )}
            {business.contact_email && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Email</dt>
                <dd className="mt-1 text-sm text-gray-900">{business.contact_email}</dd>
              </div>
            )}
            {business.contact_phone && (
              <div>
                <dt className="text-sm font-medium text-gray-500">Phone</dt>
                <dd className="mt-1 text-sm text-gray-900">{business.contact_phone}</dd>
              </div>
            )}
            <div>
              <dt className="text-sm font-medium text-gray-500">Reimbursement Timeline</dt>
              <dd className="mt-1 text-sm text-gray-900">{business.reimbursement_timeline} days</dd>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Dashboard */}
      <BusinessStatsComponent 
        business={business} 
        stats={stats} 
        loading={statsLoading}
        subscriptions={businessSubscriptions}
      />

      {/* Subscriptions Table */}
      <div className="bg-white shadow rounded-lg mt-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Subscriptions</h3>
        </div>
        <div className="border-t border-gray-200">
          <SubscriptionTable subscriptions={businessSubscriptions} />
        </div>
      </div>

      {/* Payments Table */}
      <div className="bg-white shadow rounded-lg mt-6">
        <div className="px-4 py-5 sm:px-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900">Payments</h3>
        </div>
        <div className="border-t border-gray-200">
          <PaymentTable payments={payments.filter(p => businessSubscriptions.some(s => s.id === p.subscription_id))} />
        </div>
      </div>

      {/* Edit Modal */}
      <BusinessModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        title="Edit Business"
        business={business || undefined}
        onSubmit={handleEdit}
      />

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
          <div className="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
            <div className="mt-3 text-center">
              <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100">
                <svg className="h-6 w-6 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <h3 className="text-lg leading-6 font-medium text-gray-900 mt-4">Delete Business</h3>
              <div className="mt-2 px-7 py-3">
                <p className="text-sm text-gray-500">
                  Are you sure you want to delete "{business.name}"? This action cannot be undone and will also delete all associated subscriptions and payments.
                </p>
              </div>
              <div className="items-center px-4 py-3">
                <button
                  onClick={handleDelete}
                  className="px-4 py-2 bg-red-600 text-white text-base font-medium rounded-md w-24 mr-2 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-300"
                >
                  Delete
                </button>
                <button
                  onClick={() => setIsDeleteModalOpen(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-800 text-base font-medium rounded-md w-24 hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-300"
                >
                  Cancel
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};