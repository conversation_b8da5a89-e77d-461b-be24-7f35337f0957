#!/usr/bin/env node

// <PERSON><PERSON>t to install missing type definitions for backend
const { execSync } = require('child_process');

const typesToInstall = [
  '@types/mime',
  '@types/qs', 
  '@types/range-parser',
  '@types/send',
  '@types/serve-static',
  '@types/connect',
  '@types/body-parser',
  '@types/http-errors',
  '@types/graceful-fs'
];

console.log('Installing missing type definitions...');

try {
  const command = `npm install --save-dev ${typesToInstall.join(' ')}`;
  console.log(`Running: ${command}`);
  execSync(command, { stdio: 'inherit' });
  console.log('✅ All type definitions installed successfully!');
} catch (error) {
  console.error('❌ Failed to install type definitions:', error.message);
  process.exit(1);
}