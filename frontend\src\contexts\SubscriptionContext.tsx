import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';
import { useBusiness } from './BusinessContext';

export interface Subscription {
  id: string;
  business_id: string;
  service_name: string;
  provider: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  billing_frequency: 'monthly' | 'quarterly' | 'yearly';
  category: string | null;
  status: 'active' | 'inactive' | 'cancelled';
  next_payment_date: string;
  created_at: string;
  updated_at: string;
}

export interface CreateSubscriptionData {
  business_id: string;
  service_name: string;
  provider: string;
  amount: number;
  currency: 'USD' | 'GBP' | 'KWD';
  billing_frequency: 'monthly' | 'quarterly' | 'yearly';
  category?: string;
  status?: 'active' | 'inactive' | 'cancelled';
  next_payment_date: string;
}

export interface SubscriptionFilters {
  business_id?: string;
  status?: 'active' | 'inactive' | 'cancelled';
  category?: string;
  currency?: 'USD' | 'GBP' | 'KWD';
  provider?: string;
  searchTerm?: string;
}

export interface SubscriptionCategory {
  id: string;
  name: string;
  color: string;
  icon?: string;
  description?: string;
  created_at: string;
  updated_at: string;
}

export interface SubscriptionStats {
  total: number;
  active: number;
  inactive: number;
  cancelled: number;
  monthlyCosts: Record<string, number>;
}

interface SubscriptionContextType {
  subscriptions: Subscription[];
  categories: SubscriptionCategory[];
  upcomingPayments: Subscription[];
  loading: boolean;
  error: string | null;
  filters: SubscriptionFilters;
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };

  // Actions
  fetchSubscriptions: (filters?: SubscriptionFilters, page?: number) => Promise<void>;
  createSubscription: (data: CreateSubscriptionData) => Promise<Subscription>;
  updateSubscription: (id: string, updates: Partial<CreateSubscriptionData>) => Promise<Subscription>;
  deleteSubscription: (id: string) => Promise<void>;
  bulkUpdateSubscriptions: (ids: string[], updates: Partial<Subscription>) => Promise<void>;
  bulkDeleteSubscriptions: (ids: string[]) => Promise<void>;
  searchSubscriptions: (searchTerm: string) => Promise<void>;
  getSubscriptionById: (id: string) => Promise<Subscription>;
  getBusinessStats: (businessId: string) => Promise<SubscriptionStats>;
  getUpcomingPayments: (days?: number) => Promise<void>;
  fetchCategories: () => Promise<void>;
  createCategory: (categoryData: Omit<SubscriptionCategory, 'id' | 'created_at' | 'updated_at'>) => Promise<SubscriptionCategory>;
  updateCategory: (id: string, updates: Partial<SubscriptionCategory>) => Promise<SubscriptionCategory>;
  deleteCategory: (id: string) => Promise<void>;
  setFilters: (filters: SubscriptionFilters) => void;
  clearFilters: () => void;
  calculateNextPaymentDate: (currentDate: Date, frequency: 'monthly' | 'quarterly' | 'yearly') => Date;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};

interface SubscriptionProviderProps {
  children: ReactNode;
}

export const SubscriptionProvider: React.FC<SubscriptionProviderProps> = ({ children }) => {
  const { user, session } = useAuth();
  const { currentBusiness } = useBusiness();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [categories, setCategories] = useState<SubscriptionCategory[]>([]);
  const [upcomingPayments, setUpcomingPayments] = useState<Subscription[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filters, setFiltersState] = useState<SubscriptionFilters>({});
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20
  });

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const getAuthHeaders = () => ({
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${session?.access_token}`
  });

  const fetchSubscriptions = async (newFilters?: SubscriptionFilters, page: number = 1) => {
    if (!user || !session) return;

    setLoading(true);
    setError(null);

    try {
      const activeFilters = { ...filters, ...newFilters };
      const offset = (page - 1) * pagination.itemsPerPage;
      
      const queryParams = new URLSearchParams();
      queryParams.append('limit', pagination.itemsPerPage.toString());
      queryParams.append('offset', offset.toString());
      
      if (activeFilters.business_id) queryParams.append('business_id', activeFilters.business_id);
      if (activeFilters.status) queryParams.append('status', activeFilters.status);
      if (activeFilters.category) queryParams.append('category', activeFilters.category);
      if (activeFilters.currency) queryParams.append('currency', activeFilters.currency);
      if (activeFilters.provider) queryParams.append('provider', activeFilters.provider);
      if (activeFilters.searchTerm) queryParams.append('q', activeFilters.searchTerm);

      const response = await fetch(`${API_BASE_URL}/subscriptions?${queryParams}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscriptions');
      }

      const result = await response.json();
      setSubscriptions(result.data.subscriptions);
      
      setPagination(prev => ({
        ...prev,
        currentPage: page,
        totalItems: result.data.total,
        totalPages: Math.ceil(result.data.total / prev.itemsPerPage)
      }));

      if (newFilters) {
        setFiltersState(activeFilters);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch subscriptions');
    } finally {
      setLoading(false);
    }
  };

  const createSubscription = async (data: CreateSubscriptionData): Promise<Subscription> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create subscription');
      }

      const result = await response.json();
      const newSubscription = result.data;

      setSubscriptions(prev => [newSubscription, ...prev]);
      
      // Refresh categories if a new category was added
      if (data.category && !categories.some(cat => cat.name === data.category)) {
        await fetchCategories();
      }

      return newSubscription;
    } catch (err) {
      throw err;
    }
  };

  const updateSubscription = async (id: string, updates: Partial<CreateSubscriptionData>): Promise<Subscription> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update subscription');
      }

      const result = await response.json();
      const updatedSubscription = result.data;

      setSubscriptions(prev => prev.map(s => s.id === id ? updatedSubscription : s));

      // Refresh categories if category was updated
      if (updates.category) {
        await fetchCategories();
      }

      return updatedSubscription;
    } catch (err) {
      throw err;
    }
  };

  const deleteSubscription = async (id: string): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete subscription');
      }

      setSubscriptions(prev => prev.filter(s => s.id !== id));
    } catch (err) {
      throw err;
    }
  };

  const bulkUpdateSubscriptions = async (ids: string[], updates: Partial<Subscription>): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/bulk-update`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ ids, updates })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update subscriptions');
      }

      const result = await response.json();
      const updatedSubscriptions = result.data;

      setSubscriptions(prev =>
        prev.map(subscription => {
          const updated = updatedSubscriptions.find((u: Subscription) => u.id === subscription.id);
          return updated || subscription;
        })
      );
    } catch (err) {
      throw err;
    }
  };

  const bulkDeleteSubscriptions = async (ids: string[]): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/bulk-delete`, {
        method: 'DELETE',
        headers: getAuthHeaders(),
        body: JSON.stringify({ ids })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete subscriptions');
      }

      setSubscriptions(prev => prev.filter(s => !ids.includes(s.id)));
    } catch (err) {
      throw err;
    }
  };

  const searchSubscriptions = async (searchTerm: string): Promise<void> => {
    await fetchSubscriptions({ searchTerm }, 1);
  };

  const getSubscriptionById = async (id: string): Promise<Subscription> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/${id}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch subscription');
    }
  };

  const getBusinessStats = async (businessId: string): Promise<SubscriptionStats> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/business/${businessId}/stats`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business statistics');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch business statistics');
    }
  };

  const getUpcomingPayments = async (days: number = 30): Promise<void> => {
    if (!user || !session) return;

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/upcoming?days=${days}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch upcoming payments');
      }

      const result = await response.json();
      setUpcomingPayments(result.data);
    } catch (err) {
      console.error('Failed to fetch upcoming payments:', err);
    }
  };

  const fetchCategories = async (): Promise<void> => {
    if (!user || !session) return;

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/categories`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch categories');
      }

      const result = await response.json();
      setCategories(result.data);
    } catch (err) {
      console.error('Failed to fetch categories:', err);
    }
  };

  const createCategory = async (categoryData: Omit<SubscriptionCategory, 'id' | 'created_at' | 'updated_at'>): Promise<SubscriptionCategory> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/categories`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(categoryData)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create category');
      }

      const result = await response.json();
      const newCategory = result.data;

      setCategories(prev => [...prev, newCategory]);
      return newCategory;
    } catch (err) {
      throw err;
    }
  };

  const updateCategory = async (id: string, updates: Partial<SubscriptionCategory>): Promise<SubscriptionCategory> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/categories/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update category');
      }

      const result = await response.json();
      const updatedCategory = result.data;

      setCategories(prev => prev.map(c => c.id === id ? updatedCategory : c));
      return updatedCategory;
    } catch (err) {
      throw err;
    }
  };

  const deleteCategory = async (id: string): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/subscriptions/categories/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete category');
      }

      setCategories(prev => prev.filter(c => c.id !== id));
    } catch (err) {
      throw err;
    }
  };

  const setFilters = (newFilters: SubscriptionFilters) => {
    setFiltersState(newFilters);
    fetchSubscriptions(newFilters, 1);
  };

  const clearFilters = () => {
    setFiltersState({});
    fetchSubscriptions({}, 1);
  };

  const calculateNextPaymentDate = (currentDate: Date, frequency: 'monthly' | 'quarterly' | 'yearly'): Date => {
    const nextDate = new Date(currentDate);
    
    switch (frequency) {
      case 'monthly':
        nextDate.setMonth(nextDate.getMonth() + 1);
        break;
      case 'quarterly':
        nextDate.setMonth(nextDate.getMonth() + 3);
        break;
      case 'yearly':
        nextDate.setFullYear(nextDate.getFullYear() + 1);
        break;
    }
    
    return nextDate;
  };

  // Auto-fetch subscriptions when user or current business changes
  useEffect(() => {
    if (user && session) {
      fetchSubscriptions();
      fetchCategories();
      getUpcomingPayments();
    } else {
      setSubscriptions([]);
      setCategories([]);
      setUpcomingPayments([]);
    }
  }, [user, session]);

  // Filter by current business when it changes
  useEffect(() => {
    if (currentBusiness) {
      setFilters({ business_id: currentBusiness.id });
    } else {
      clearFilters();
    }
  }, [currentBusiness]);

  const value: SubscriptionContextType = {
    subscriptions,
    categories,
    upcomingPayments,
    loading,
    error,
    filters,
    pagination,
    fetchSubscriptions,
    createSubscription,
    updateSubscription,
    deleteSubscription,
    bulkUpdateSubscriptions,
    bulkDeleteSubscriptions,
    searchSubscriptions,
    getSubscriptionById,
    getBusinessStats,
    getUpcomingPayments,
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    setFilters,
    clearFilters,
    calculateNextPaymentDate
  };

  return (
    <SubscriptionContext.Provider value={value}>
      {children}
    </SubscriptionContext.Provider>
  );
};