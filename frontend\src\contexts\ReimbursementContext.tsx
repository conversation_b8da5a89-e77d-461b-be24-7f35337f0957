import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from './AuthContext';

export interface Reimbursement {
  id: string;
  business_id: string;
  user_id: string;
  reference_number: string;
  amount: number;
  currency: 'KWD';
  description: string | null;
  request_date: string;
  expected_date: string;
  actual_date: string | null;
  status: 'pending' | 'approved' | 'received';
  created_at: string;
  updated_at: string;
  business: {
    id: string;
    name: string;
    reimbursement_timeline: number;
  };
  payments: {
    id: string;
    amount: number;
    currency: string;
    payment_date: string;
    subscription: {
      service_name: string;
      provider: string;
    };
  }[];
}

export interface ReimbursementStatusChange {
  id: string;
  reimbursement_id: string;
  from_status: 'pending' | 'approved' | 'received';
  to_status: 'pending' | 'approved' | 'received';
  changed_by: string;
  notes: string | null;
  created_at: string;
}

export interface CreateReimbursementData {
  business_id: string;
  payment_ids: string[];
  amount?: number;
  description?: string;
  expected_date?: string;
}

export interface ReimbursementFilters {
  business_id?: string;
  status?: 'pending' | 'approved' | 'received';
  date_from?: string;
  date_to?: string;
  amount_min?: number;
  amount_max?: number;
}

export interface ReimbursementStats {
  total: number;
  pending: number;
  approved: number;
  received: number;
  totalAmount: number;
  pendingAmount: number;
  approvedAmount: number;
  receivedAmount: number;
  overdue: number;
}

interface ReimbursementContextType {
  reimbursements: Reimbursement[];
  loading: boolean;
  error: string | null;
  pagination: {
    total: number;
    limit: number;
    offset: number;
    hasMore: boolean;
  };
  fetchReimbursements: (filters?: ReimbursementFilters, limit?: number, offset?: number) => Promise<void>;
  createReimbursement: (data: CreateReimbursementData) => Promise<Reimbursement>;
  updateReimbursement: (id: string, updates: Partial<Reimbursement>) => Promise<Reimbursement>;
  updateReimbursementStatus: (id: string, status: 'pending' | 'approved' | 'received', notes?: string) => Promise<Reimbursement>;
  deleteReimbursement: (id: string) => Promise<void>;
  searchReimbursements: (searchTerm: string, filters?: ReimbursementFilters) => Promise<Reimbursement[]>;
  getBusinessStats: (businessId: string) => Promise<ReimbursementStats>;
  getReimbursementHistory: (id: string) => Promise<ReimbursementStatusChange[]>;
  bulkUpdateStatus: (ids: string[], status: 'pending' | 'approved' | 'received', notes?: string) => Promise<any>;
  bulkDeleteReimbursements: (ids: string[]) => Promise<any>;
  exportReimbursements: (filters?: ReimbursementFilters, format?: 'csv' | 'pdf') => Promise<void>;
}

const ReimbursementContext = createContext<ReimbursementContextType | undefined>(undefined);

export const useReimbursement = () => {
  const context = useContext(ReimbursementContext);
  if (context === undefined) {
    throw new Error('useReimbursement must be used within a ReimbursementProvider');
  }
  return context;
};

interface ReimbursementProviderProps {
  children: ReactNode;
}

export const ReimbursementProvider: React.FC<ReimbursementProviderProps> = ({ children }) => {
  const { user, session } = useAuth();
  const [reimbursements, setReimbursements] = useState<Reimbursement[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 50,
    offset: 0,
    hasMore: false
  });

  const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

  const getAuthHeaders = () => ({
    'Authorization': `Bearer ${session?.access_token}`,
    'Content-Type': 'application/json'
  });

  const fetchReimbursements = async (
    filters: ReimbursementFilters = {},
    limit: number = 50,
    offset: number = 0
  ) => {
    if (!user || !session) return;

    setLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams({
        limit: limit.toString(),
        offset: offset.toString(),
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`${API_BASE_URL}/reimbursements?${queryParams}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch reimbursements');
      }

      const result = await response.json();
      setReimbursements(result.data);
      setPagination(result.pagination);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch reimbursements');
    } finally {
      setLoading(false);
    }
  };

  const createReimbursement = async (data: CreateReimbursementData): Promise<Reimbursement> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify(data)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create reimbursement');
      }

      const result = await response.json();
      const newReimbursement = result.data;

      setReimbursements(prev => [newReimbursement, ...prev]);
      return newReimbursement;
    } catch (err) {
      throw err;
    }
  };

  const updateReimbursement = async (
    id: string,
    updates: Partial<Reimbursement>
  ): Promise<Reimbursement> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/${id}`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify(updates)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update reimbursement');
      }

      const result = await response.json();
      const updatedReimbursement = result.data;

      setReimbursements(prev => 
        prev.map(r => r.id === id ? updatedReimbursement : r)
      );
      return updatedReimbursement;
    } catch (err) {
      throw err;
    }
  };

  const updateReimbursementStatus = async (
    id: string,
    status: 'pending' | 'approved' | 'received',
    notes?: string
  ): Promise<Reimbursement> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/${id}/status`, {
        method: 'PUT',
        headers: getAuthHeaders(),
        body: JSON.stringify({ status, notes })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update reimbursement status');
      }

      const result = await response.json();
      const updatedReimbursement = result.data;

      setReimbursements(prev => 
        prev.map(r => r.id === id ? updatedReimbursement : r)
      );
      return updatedReimbursement;
    } catch (err) {
      throw err;
    }
  };

  const deleteReimbursement = async (id: string): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/${id}`, {
        method: 'DELETE',
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete reimbursement');
      }

      setReimbursements(prev => prev.filter(r => r.id !== id));
    } catch (err) {
      throw err;
    }
  };

  const searchReimbursements = async (
    searchTerm: string,
    filters: ReimbursementFilters = {}
  ): Promise<Reimbursement[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const queryParams = new URLSearchParams({
        search: searchTerm,
        ...Object.fromEntries(
          Object.entries(filters).filter(([_, value]) => value !== undefined && value !== '')
        )
      });

      const response = await fetch(`${API_BASE_URL}/reimbursements?${queryParams}`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to search reimbursements');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to search reimbursements');
    }
  };

  const getBusinessStats = async (businessId: string): Promise<ReimbursementStats> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/business/${businessId}/stats`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch business statistics');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch business statistics');
    }
  };

  const getReimbursementHistory = async (id: string): Promise<ReimbursementStatusChange[]> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/${id}/history`, {
        headers: getAuthHeaders()
      });

      if (!response.ok) {
        throw new Error('Failed to fetch reimbursement history');
      }

      const result = await response.json();
      return result.data;
    } catch (err) {
      throw err instanceof Error ? err : new Error('Failed to fetch reimbursement history');
    }
  };

  const bulkUpdateStatus = async (
    ids: string[],
    status: 'pending' | 'approved' | 'received',
    notes?: string
  ): Promise<any> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/bulk-status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({ ids, status, notes })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update reimbursements');
      }

      const result = await response.json();

      // Refresh reimbursements after bulk update
      await fetchReimbursements();

      return result.data;
    } catch (error) {
      throw error;
    }
  };

  const bulkDeleteReimbursements = async (ids: string[]): Promise<any> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/bulk-delete`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({ ids })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete reimbursements');
      }

      const result = await response.json();

      // Refresh reimbursements after bulk delete
      await fetchReimbursements();

      return result.data;
    } catch (error) {
      throw error;
    }
  };

  const exportReimbursements = async (
    filters?: ReimbursementFilters,
    format: 'csv' | 'pdf' = 'csv'
  ): Promise<void> => {
    if (!user || !session) throw new Error('Not authenticated');

    try {
      const response = await fetch(`${API_BASE_URL}/reimbursements/export`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...getAuthHeaders()
        },
        body: JSON.stringify({ filters, format })
      });

      if (!response.ok) {
        throw new Error('Export failed');
      }

      // Handle file download
      const blob = await response.blob();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;

      const timestamp = new Date().toISOString().split('T')[0];
      const filename = `reimbursements-export-${timestamp}.${format}`;
      link.download = filename;

      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      throw error;
    }
  };

  // Fetch initial data when user logs in
  useEffect(() => {
    if (user && session) {
      fetchReimbursements();
    } else {
      setReimbursements([]);
    }
  }, [user, session]);

  const value: ReimbursementContextType = {
    reimbursements,
    loading,
    error,
    pagination,
    fetchReimbursements,
    createReimbursement,
    updateReimbursement,
    updateReimbursementStatus,
    deleteReimbursement,
    searchReimbursements,
    getBusinessStats,
    getReimbursementHistory,
    bulkUpdateStatus,
    bulkDeleteReimbursements,
    exportReimbursements
  };

  return (
    <ReimbursementContext.Provider value={value}>
      {children}
    </ReimbursementContext.Provider>
  );
};