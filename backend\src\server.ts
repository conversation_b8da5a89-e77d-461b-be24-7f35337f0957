import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import rateLimit from 'express-rate-limit';
import compression from 'compression';
import { config } from './config/config';
import { errorHandler } from './middleware/errorHandler';
import { 
  sanitizeInput, 
  securityHeaders, 
  securityLogging, 
  devSecurityMiddleware, 
  prodSecurityMiddleware,
  apiVersioning 
} from './middleware/security';
import { authRoutes } from './routes/auth';
import { businessRoutes } from './routes/business';
import { subscriptionRoutes } from './routes/subscription';
import { paymentRoutes } from './routes/payment';
import { reimbursementRoutes } from './routes/reimbursement';
import { dashboardRoutes } from './routes/dashboard';
import { reportRoutes } from './routes/report';
import { exchangeRateRoutes } from './routes/exchangeRate';
import { ExchangeRateJob } from './jobs/exchangeRateJob';
import { 
  performanceTracking, 
  memoryMonitoring, 
  requestTimeout, 
  healthMetrics,
  setupProcessHandlers,
  gracefulShutdown 
} from './middleware/performance';

const app = express();

// Setup process error handlers
setupProcessHandlers();

// Trust proxy for production deployments behind reverse proxy
if (config.NODE_ENV === 'production') {
  app.set('trust proxy', 1);
}

// Compression middleware for performance
app.use(compression({
  filter: (req, res) => {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  },
  level: 6,
  threshold: 1024
}));

// Enhanced security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      scriptSrc: ["'self'"],
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false,
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
}));

// CORS configuration with security considerations
app.use(cors({
  origin: function (origin, callback) {
    const allowedOrigins = [
      config.FRONTEND_URL,
      'http://localhost:3000',
      'http://127.0.0.1:3000'
    ];
    
    // Allow requests with no origin (like mobile apps or Postman)
    if (!origin) return callback(null, true);
    
    if (allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  exposedHeaders: ['X-Total-Count'],
  maxAge: 86400 // 24 hours
}));

// Enhanced rate limiting with different limits for different routes
const createRateLimiter = (windowMs: number, max: number, message: string) => 
  rateLimit({
    windowMs,
    max,
    message: { error: message },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Skip rate limiting for health checks
      return req.path === '/health';
    }
  });

// General API rate limiting
app.use('/api/', createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100, // 100 requests per window
  'Too many requests from this IP, please try again later.'
));

// Stricter rate limiting for authentication routes
app.use('/api/auth', createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  20, // 20 requests per window
  'Too many authentication attempts, please try again later.'
));

// Very strict rate limiting for password reset
app.use('/api/auth/reset-password', createRateLimiter(
  60 * 60 * 1000, // 1 hour
  5, // 5 requests per hour
  'Too many password reset attempts, please try again later.'
));

// Apply security middleware
app.use(securityHeaders);
app.use(securityLogging);
app.use(devSecurityMiddleware);
app.use(prodSecurityMiddleware);
app.use(apiVersioning);

// Apply performance middleware
app.use(performanceTracking);
app.use(memoryMonitoring);
app.use(requestTimeout(30000)); // 30 second timeout

// Body parsing middleware with security considerations
app.use(express.json({ 
  limit: '10mb',
  verify: (req: express.Request & { rawBody?: Buffer }, res, buf) => {
    // Store raw body for webhook signature verification if needed
    req.rawBody = buf;
  }
}));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Input sanitization (apply after body parsing)
app.use(sanitizeInput);

// Health check endpoints
app.get('/health', (req, res) => {
  res.status(200).json({ status: 'OK', timestamp: new Date().toISOString() });
});

// Detailed health metrics endpoint
app.get('/health/metrics', healthMetrics);

// API routes
app.use('/api/auth', authRoutes);
app.use('/api/businesses', businessRoutes);
app.use('/api/subscriptions', subscriptionRoutes);
app.use('/api/payments', paymentRoutes);
app.use('/api/reimbursements', reimbursementRoutes);
app.use('/api/dashboard', dashboardRoutes);
app.use('/api/reports', reportRoutes);
app.use('/api/exchange-rates', exchangeRateRoutes);

// Error handling middleware
app.use(errorHandler);

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({ error: 'Route not found' });
});

const PORT = process.env.PORT || 5000;

const server = app.listen(PORT, () => {
  console.log(`🚀 Server running on port ${PORT}`);
  console.log(`🌍 Environment: ${config.NODE_ENV}`);
  console.log(`🔗 Frontend URL: ${config.FRONTEND_URL}`);
  console.log(`🏥 Health check: http://localhost:${PORT}/health`);
  console.log(`📊 Metrics: http://localhost:${PORT}/health/metrics`);
  
  // Start exchange rate job
  ExchangeRateJob.start();
  console.log('💱 Exchange rate job initialized');
  
  // Log security status
  console.log('🔒 Security features enabled:');
  console.log('  ✅ Helmet security headers');
  console.log('  ✅ CORS protection');
  console.log('  ✅ Rate limiting');
  console.log('  ✅ Input sanitization');
  console.log('  ✅ Request timeout');
  console.log('  ✅ Performance monitoring');
  
  if (config.NODE_ENV === 'production') {
    console.log('  ✅ Production security mode');
    console.log('  ✅ Compression enabled');
    console.log('  ✅ HSTS enabled');
  }
});

// Setup graceful shutdown
gracefulShutdown(server);

export default app;