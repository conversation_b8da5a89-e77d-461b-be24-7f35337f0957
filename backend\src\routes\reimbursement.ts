import { Router } from 'express';
import { z } from 'zod';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { ReimbursementService } from '../services/reimbursementService';
import { requireAuth } from '../middleware/auth';

const router = Router();

// Apply authentication middleware to all routes
router.use(requireAuth);

const createReimbursementSchema = z.object({
  business_id: z.string().uuid('Invalid business ID'),
  payment_ids: z.array(z.string().uuid()).min(1, 'At least one payment must be included'),
  amount: z.number().positive('Amount must be positive').optional(),
  description: z.string().optional(),
  expected_date: z.string().datetime().optional()
});

const updateReimbursementSchema = z.object({
  amount: z.number().positive('Amount must be positive').optional(),
  description: z.string().optional(),
  expected_date: z.string().datetime().optional(),
  actual_date: z.string().datetime().optional()
});

const statusUpdateSchema = z.object({
  status: z.enum(['pending', 'approved', 'received']),
  notes: z.string().optional()
});

const filtersSchema = z.object({
  business_id: z.string().uuid().optional(),
  status: z.enum(['pending', 'approved', 'received']).optional(),
  date_from: z.string().datetime().optional(),
  date_to: z.string().datetime().optional(),
  amount_min: z.number().positive().optional(),
  amount_max: z.number().positive().optional(),
  limit: z.number().int().min(1).max(100).default(50),
  offset: z.number().int().min(0).default(0),
  search: z.string().optional()
});

// GET /api/reimbursements
router.get('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const filters = filtersSchema.parse(req.query);
  
  if (filters.search) {
    const reimbursements = await ReimbursementService.searchReimbursements(
      userId,
      filters.search,
      filters
    );
    
    res.json({
      success: true,
      data: reimbursements,
      pagination: {
        total: reimbursements.length,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: false
      }
    });
  } else {
    const result = await ReimbursementService.getUserReimbursements(
      userId,
      filters,
      filters.limit,
      filters.offset
    );

    res.json({
      success: true,
      data: result.reimbursements,
      pagination: {
        total: result.total,
        limit: filters.limit,
        offset: filters.offset,
        hasMore: filters.offset + filters.limit < result.total
      }
    });
  }
}));

// GET /api/reimbursements/:id
router.get('/:id', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  const reimbursement = await ReimbursementService.getReimbursementById(id, userId);
  
  res.json({
    success: true,
    data: reimbursement
  });
}));

// GET /api/reimbursements/:id/history
router.get('/:id/history', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  const history = await ReimbursementService.getReimbursementHistory(id, userId);
  
  res.json({
    success: true,
    data: history
  });
}));

// POST /api/reimbursements
router.post('/', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const reimbursementData = createReimbursementSchema.parse(req.body);
  
  const reimbursement = await ReimbursementService.createReimbursement(userId, reimbursementData);
  
  res.status(201).json({
    success: true,
    message: 'Reimbursement created successfully',
    data: reimbursement
  });
}));

// PUT /api/reimbursements/:id
router.put('/:id', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const updates = updateReimbursementSchema.parse(req.body);
  
  const reimbursement = await ReimbursementService.updateReimbursement(id, userId, updates);
  
  res.json({
    success: true,
    message: 'Reimbursement updated successfully',
    data: reimbursement
  });
}));

// PUT /api/reimbursements/:id/status
router.put('/:id/status', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  const { status, notes } = statusUpdateSchema.parse(req.body);
  
  const reimbursement = await ReimbursementService.updateReimbursementStatus(
    id,
    userId,
    status,
    notes
  );
  
  res.json({
    success: true,
    message: 'Reimbursement status updated successfully',
    data: reimbursement
  });
}));

// DELETE /api/reimbursements/:id
router.delete('/:id', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { id } = req.params;
  
  await ReimbursementService.deleteReimbursement(id, userId);
  
  res.json({
    success: true,
    message: 'Reimbursement deleted successfully'
  });
}));

// GET /api/reimbursements/business/:businessId/stats
router.get('/business/:businessId/stats', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { businessId } = req.params;

  const stats = await ReimbursementService.getBusinessReimbursementStats(businessId, userId);

  res.json({
    success: true,
    data: stats
  });
}));

// PUT /api/reimbursements/bulk-status
router.put('/bulk-status', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { ids, status, notes } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('IDs array is required');
  }

  if (!['pending', 'approved', 'received'].includes(status)) {
    throw new Error('Invalid status');
  }

  const results = await ReimbursementService.bulkUpdateStatus(ids, userId, status, notes);

  res.json({
    success: true,
    message: 'Bulk status update completed',
    data: results
  });
}));

// DELETE /api/reimbursements/bulk-delete
router.delete('/bulk-delete', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { ids } = req.body;

  if (!Array.isArray(ids) || ids.length === 0) {
    throw new Error('IDs array is required');
  }

  const results = await ReimbursementService.bulkDeleteReimbursements(ids, userId);

  res.json({
    success: true,
    message: 'Bulk delete completed',
    data: results
  });
}));

// POST /api/reimbursements/export
router.post('/export', asyncHandler(async (req, res) => {
  const userId = req.user!.id;
  const { filters, format = 'csv' } = req.body;

  const result = await ReimbursementService.exportReimbursements(userId, filters, format);

  const contentType = format === 'csv' ? 'text/csv' : 'text/plain';
  const filename = `reimbursements-export-${new Date().toISOString().split('T')[0]}.${format}`;

  res.setHeader('Content-Type', contentType);
  res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
  res.send(result);
}));

export { router as reimbursementRoutes };